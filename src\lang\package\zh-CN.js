import locals from '@/lang/package/zh-CN/index.js'
export default {
  ...locals,
  // 路由国际化
  route: {
    workbench: "工作台",
    afterSales: "售后",
    department: "部门",
    departmentManagement: '部门管理',
    order: "订单",
    orderManagement: '订购单',
    orderDetail: '订购单详情',
    orderDeliverySchedule: '交货计划',
    orderPurchase: '采购单',
    orderPurchaseDetail: '采购单详情',
    orderPurchaseCreate: '创建采购单',
    orderPurchaseSubmit: '确认采购单',
    logistics: '物流',
    logisticsAirTransport: '航空运输',
    logisticsAirTransportDetail: '航空运输详情',
    logisticsThirdParty: '航空运输详情',
    LogisticsDelivery: '装箱发货',
    LogisticsDeliveryInfo: '装箱发货明细',
    LogisticsProcurementShipping: '采购收运',
    LogisticsProcurementShippingInfo: '采购收运明细',
    afterSalesReasons: "售后原因",
    afterSalesManagement: "售后管理",
    afterSalesApplication: "申请售后",
    afterSalesDetail: '售后详情',
    organization: "组织",
    accountManagement: "账号管理",
    roleManagement: "角色管理",
    goodsName: '商品管理',
    purchasingManagement: '原材料',
    purchasingClassification: '产品分类',
    finishedManagement: '成品管理',
    addPurchasingProduct: '新增原材料',
    finance: '财务',
    invoiceManagement: '发票管理',
    invoiceDetail: '发票详情',
    accountsReceivable: '应收账款',
    receivableDetail:'应收账款明细',
    accountsPayable: '应付账款',
    accountsPayDetail:'应付账款明细',
    purchaseRefundPayBill:'采购退单',
    purchaseRefundDetail:'采购退单明细',
    salesManagement: '销售商品',
    salesClassification: '商品分类',
    addSalesGoods: '添加销售商品',
    pendingPurchase: '待客户采购',
    channel: '渠道',
    originSupplier: '供应商',
    portManagement: '港口管理',
    customerManagement: '客户管理',
    usersManagement: '用户管理',
    warehouseManagement: '仓库管理',
    addSupplier: '添加供应商',
    traceability: '溯源',
    collectTemp: '采集模板',
    traceNode: '溯源节点',
    addCollectTemp: '添加采集模板',
    versionManage:'版本管理',
    addCustomer: '新增客户',
    customerAuthentication: '客户认证审核',
    operation:'运营',
    serviceTerms:'服务条款',
    homepage:'首页配置',
    pendingOrder: '代下单',
    purchaseContractManagement: '采购合同',
    addPurchaseContract: '创建采购合同',
    addPurchaseAgreement: '添加采购协议',
    salesContract: '销售合同',
    salesContractManagement: '销售合同',
    addSalesContract: '创建销售合同',
    addSalesAgreement: '添加销售协议',
    customerManagementDetails: '客户详情',
    deliveryManagement: '提货管理',
    deliveryManagementDetails: '提货详情',
    inventory: '库存',
    rawMaterialProcess: '原料加工',
    rawMaterialInventory: '原料仓',
    finishedProductInventory: '成品仓',
    goodsCheck: '货物抽检',
    goodsCheckDetails: '货物抽检详情',
    goodsSpotCheck: '货物抽检',
    tenantsManagement: '租户管理',
    departManagement: '部门管理',
    tenantsManagementAdd: '新增租户',
    tenantsManagementUpdate: '编辑租户',
    tenantsManagementDetail: '租户详情',
    menuManagement: '菜单管理',
    menuAuthority: '权限管理',
    tenantMenuAuthority: '租户菜单权限管理',
    tenantMenuMg: '租户菜单管理',
    // tenantsManagementDetail: '租户详情',
    applicationConsultingManagement:'应用咨询管理',

  },
  afterSales: {
    addReasonsTitle: "@:common.add@:route.afterSalesReasons",
    editReasonsTitle: "@:common.edit@:route.afterSalesReasons",
  },
  common: {
    screenfull: '全屏/窗口',
    statusEmun: {
        'all':	'全部',
        'enable':	'启用',
        'disable':	'禁用',
    },
    placeholder: {
        'inputTips': '请输入',
        'selectTips': '请选择',
    },
    add: '添加',
    edit: '编辑',
    delete: '删除',
    batchDelete: '批量删除',
    // activeBtn: '正常',
    // inactiveBtn: '停用',
    activeBtn: '启用',
    inactiveBtn: '禁用',
    cancel: '取消',
    confirm: '确认',
    tipTitle: '提示',
    search: '搜索',
    reset: '重置',
    handle: '操作',
    import: '导入',
    export: '导出',
    view: '查看',
    download: '下载',
    deleteTips: '删除成功！',
    successTips: '操作成功！',
    saveTips: '保存成功！',
    updateTips: '更新成功！',
    payType: {
      '1': '授信支付'
    },
    copy:'复制',
    goBack: '返回',
    detail: '详情',
    cancel: '取消',
    confirm: '确认',
    resetpassword: '重置密码',
    edit: '编辑',
    exportTips:
      "已创建导出任务，导出任务正在进行，等待导出完成后，再进行下载！",
    export: "导出",
  },
  // 登录页面国际化
  login: {
    username: "账号",
    mobile:"手机号",
    password: "密码",
    forgotPassword:"忘记密码",
    login: "登 录",
    captchaCode: "验证码",
    capsLock: "大写锁定已打开",
    contactAdmin:"请联系管理员处理！",
    message: {
      username: {
        required: "请输入账号",
      },
      password: {
        required: "请输入密码",
        min: "密码不能少于6位",
      },
      captchaCode: {
        required: "请输入验证码",
      },
      mobile:{
        required: "请输入手机号",
      }
    },
  },
  langSelect: {
    message: {
      success: "切换语言成功！",
    },
  },

  roleObj: {
    roleName: '角色名称',
    sort: '排序',
    status: '状态',
    createTime: '创建时间',
    operate: '操作'
  },

  // roleManagement:{
  //   permission: '权限设置',
  //   roleStatus: '角色状态',
  //   roleDesc:'备注',
  //   addRoleTitle: '新增角色',
  //   editRoleTitle: '编辑角色',
  //   delRoleTip: '此操作将删除该角色，是否继续?'
  // },
  user: {
    profile: '个人中心',
    logout: '退出登录',
  },
 /* userObj:{
    name: '姓名',
    nickNameOrPhone: '用户',
    userStatus: '用户状态',
    role: '角色',
    userRole: '角色权限',
    createTime: '创建时间',
    phone: '手机号',
    employeeNo: '工号',
    sex: '性别',
    sex1: '男',
    sex2: '女'
  },
  userManagement: {
    baseInfo: '基本信息',
    permissionInfo: '权限信息',
    operate: '操作',
    addUser: '添加人员',
    resetPassword: '重置密码',
    addUserTitle: '添加人员',
    editUserTitle: '编辑人员',
    delUserTip: '此操作将删除该用户，是否继续?',
    nickNameOrPhoneInput: '请输入姓名或手机号',
    nameInput: '请输入姓名',
    phoneInput: '请输入手机号',
    employeeNoInput: '请输入工号(数字格式）',
    sexInput: '请选择性别',
    roleInput: '请选择角色',
    lastPhoneInput: '请输入手机号',
    statusInput: '请选择状态'
  }*/
};
